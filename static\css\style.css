/* Arabic font import */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* General styles */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
}

/* RTL specific adjustments */
.dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
}

/* Custom navbar styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.navbar-brand {
    font-weight: 700;
}

/* Card styles */
.card {
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,.125);
    padding: 1rem;
}

/* Form styles */
.form-control {
    border-radius: 5px;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25);
}

/* Button styles */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1rem;
}

/* Table styles */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

/* Dashboard widgets */
.dashboard-widget {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
}

.dashboard-widget .icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.dashboard-widget .title {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.dashboard-widget .value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #0d6efd;
}

/* Alert styles */
.alert {
    border-radius: 10px;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white;
    }
    
    .container {
        width: 100%;
        max-width: none;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .dashboard-widget {
        padding: 1rem;
    }
}