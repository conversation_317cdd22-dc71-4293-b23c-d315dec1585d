# Generated by Django 5.1.6 on 2025-03-02 21:39

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=20, unique=True, verbose_name='Invoice Number')),
                ('date', models.DateField(verbose_name='Invoice Date')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=10, verbose_name='Status')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Subtotal')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Tax Amount')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Discount Amount')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Total Amount')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_invoices', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Purchase Invoice',
                'verbose_name_plural': 'Purchase Invoices',
                'ordering': ['-date', '-number'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseInvoiceLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Unit Price')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Tax Rate')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Discount Amount')),
                ('total', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Total')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='purchases.purchaseinvoice')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_lines', to='inventory.product')),
            ],
            options={
                'verbose_name': 'Purchase Invoice Line',
                'verbose_name_plural': 'Purchase Invoice Lines',
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Supplier Code')),
                ('name', models.CharField(max_length=200, verbose_name='Supplier Name')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='Contact Person')),
                ('phone', models.CharField(max_length=20, verbose_name='Phone')),
                ('mobile', models.CharField(blank=True, max_length=20, verbose_name='Mobile')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('address', models.TextField(verbose_name='Address')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='Tax Number')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Credit Limit')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Current Balance')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='suppliers', to='accounts.account')),
            ],
            options={
                'verbose_name': 'Supplier',
                'verbose_name_plural': 'Suppliers',
                'ordering': ['code'],
            },
        ),
        migrations.AddField(
            model_name='purchaseinvoice',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_invoices', to='purchases.supplier'),
        ),
        migrations.CreateModel(
            name='SupplierPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=20, unique=True, verbose_name='Payment Number')),
                ('date', models.DateField(verbose_name='Payment Date')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('bank', 'Bank Transfer'), ('cheque', 'Cheque')], max_length=10, verbose_name='Payment Method')),
                ('reference', models.CharField(blank=True, max_length=50, verbose_name='Reference')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=10, verbose_name='Status')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='supplier_payments', to=settings.AUTH_USER_MODEL)),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='supplier_payments', to='purchases.supplier')),
            ],
            options={
                'verbose_name': 'Supplier Payment',
                'verbose_name_plural': 'Supplier Payments',
                'ordering': ['-date', '-number'],
            },
        ),
    ]
