{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}تسجيل الدخول{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center mt-5">
        <div class="col-md-6 col-lg-4">
            <div class="card">
                <div class="card-header text-center">
                    <h3 class="mb-2 text-primary">الإشرافي</h3>
                    <h4 class="mb-0">تسجيل الدخول</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {{ form|crispy }}
                        
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                اسم المستخدم أو كلمة المرور غير صحيحة
                            </div>
                        {% endif %}
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    body {
        background-color: #f8f9fa;
    }
    .card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    .card-header {
        background-color: white;
        border-bottom: 1px solid rgba(0,0,0,.125);
        padding: 1.5rem;
    }
    .card-body {
        padding: 2rem;
    }
    .form-group {
        margin-bottom: 1rem;
    }
    .alert {
        margin-bottom: 1rem;
    }
</style>
{% endblock %}