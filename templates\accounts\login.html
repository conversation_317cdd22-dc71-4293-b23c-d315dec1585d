{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}تسجيل الدخول{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card">
                    <div class="card-header text-center">
                        <i class="fas fa-chart-line fa-3x mb-3" style="color: #ffffff;"></i>
                        <h2 class="mb-2" style="color: #ffffff; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">الإشرافي</h2>
                        <h5 class="mb-0" style="color: #b0b0b0;">نظام المحاسبة المتقدم</h5>
                        <hr style="border-color: #404040;">
                        <h4 class="mb-0" style="color: #ffffff;">تسجيل الدخول</h4>
                    </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {{ form|crispy }}
                        
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                اسم المستخدم أو كلمة المرور غير صحيحة
                            </div>
                        {% endif %}
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}