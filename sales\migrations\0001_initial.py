# Generated by Django 5.1.6 on 2025-03-02 21:39

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Customer Code')),
                ('name', models.CharField(max_length=200, verbose_name='Customer Name')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='Contact Person')),
                ('phone', models.Char<PERSON>ield(max_length=20, verbose_name='Phone')),
                ('mobile', models.Char<PERSON><PERSON>(blank=True, max_length=20, verbose_name='Mobile')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('address', models.TextField(verbose_name='Address')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='Tax Number')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Credit Limit')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Current Balance')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Discount Percentage')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='customers', to='accounts.account')),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='CustomerPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=20, unique=True, verbose_name='Payment Number')),
                ('date', models.DateField(verbose_name='Payment Date')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('bank', 'Bank Transfer'), ('cheque', 'Cheque'), ('card', 'Credit/Debit Card')], max_length=10, verbose_name='Payment Method')),
                ('reference', models.CharField(blank=True, max_length=50, verbose_name='Reference')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=10, verbose_name='Status')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='customer_payments', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='customer_payments', to='sales.customer')),
            ],
            options={
                'verbose_name': 'Customer Payment',
                'verbose_name_plural': 'Customer Payments',
                'ordering': ['-date', '-number'],
            },
        ),
        migrations.CreateModel(
            name='SalesInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=20, unique=True, verbose_name='Invoice Number')),
                ('date', models.DateField(verbose_name='Invoice Date')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=10, verbose_name='Status')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Subtotal')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Tax Amount')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Discount Amount')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Total Amount')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_invoices', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_invoices', to='sales.customer')),
            ],
            options={
                'verbose_name': 'Sales Invoice',
                'verbose_name_plural': 'Sales Invoices',
                'ordering': ['-date', '-number'],
            },
        ),
        migrations.CreateModel(
            name='SalesInvoiceLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Unit Price')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Tax Rate')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Discount Amount')),
                ('total', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Total')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='sales.salesinvoice')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sale_lines', to='inventory.product')),
            ],
            options={
                'verbose_name': 'Sales Invoice Line',
                'verbose_name_plural': 'Sales Invoice Lines',
            },
        ),
        migrations.CreateModel(
            name='SalesReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=20, unique=True, verbose_name='Return Number')),
                ('date', models.DateField(verbose_name='Return Date')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=10, verbose_name='Status')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Total Amount')),
                ('reason', models.TextField(verbose_name='Return Reason')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_returns', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_returns', to='sales.customer')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='returns', to='sales.salesinvoice')),
            ],
            options={
                'verbose_name': 'Sales Return',
                'verbose_name_plural': 'Sales Returns',
                'ordering': ['-date', '-number'],
            },
        ),
        migrations.CreateModel(
            name='SalesReturnLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Unit Price')),
                ('total', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Total')),
                ('invoice_line', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='return_lines', to='sales.salesinvoiceline')),
                ('sales_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='sales.salesreturn')),
            ],
            options={
                'verbose_name': 'Sales Return Line',
                'verbose_name_plural': 'Sales Return Lines',
            },
        ),
    ]
