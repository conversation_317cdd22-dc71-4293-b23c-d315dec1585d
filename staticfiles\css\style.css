/* Arabic font import */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* Dark Theme Variables */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    --accent-color: #ffffff;
    --border-color: #404040;
    --shadow-dark: 0 4px 20px rgba(0, 0, 0, 0.3);
    --shadow-light: 0 2px 10px rgba(255, 255, 255, 0.1);
}

/* General styles */
body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    min-height: 100vh;
}

/* RTL specific adjustments */
.dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
}

/* Custom navbar styles */
.navbar {
    background: linear-gradient(90deg, #000000 0%, #2d2d2d 100%) !important;
    box-shadow: var(--shadow-dark);
    border-bottom: 1px solid var(--border-color);
}

.navbar-brand {
    font-weight: 700;
    color: var(--text-primary) !important;
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--text-primary) !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Card styles */
.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    box-shadow: var(--shadow-dark);
    margin-bottom: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

.card-header {
    background: linear-gradient(90deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

/* Form styles */
.form-control {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-control:focus {
    background: var(--bg-secondary);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

/* Button styles */
.btn {
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #000000 0%, #404040 100%);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #404040 0%, #000000 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* Table styles */
.table {
    background: var(--bg-secondary);
    border-radius: 15px;
    overflow: hidden;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.table thead th {
    background: var(--bg-tertiary);
    border-bottom: 2px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
}

.table tbody tr {
    border-bottom: 1px solid var(--border-color);
    transition: background 0.3s ease;
}

.table tbody tr:hover {
    background: var(--bg-tertiary);
}

/* Dashboard widgets */
.dashboard-widget {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-dark);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffffff 0%, #888888 100%);
}

.dashboard-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

.dashboard-widget .icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.dashboard-widget .title {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.dashboard-widget .value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-primary);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Alert styles */
.alert {
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.alert-danger {
    background: linear-gradient(135deg, #2d1b1b 0%, #3d2020 100%);
    border-color: #5a2d2d;
    color: #ffcccc;
}

.alert-success {
    background: linear-gradient(135deg, #1b2d1b 0%, #203d20 100%);
    border-color: #2d5a2d;
    color: #ccffcc;
}

.alert-warning {
    background: linear-gradient(135deg, #2d2d1b 0%, #3d3d20 100%);
    border-color: #5a5a2d;
    color: #ffffcc;
}

/* Dropdown styles */
.dropdown-menu {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    box-shadow: var(--shadow-dark);
}

.dropdown-item {
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Footer styles */
.footer {
    background: linear-gradient(90deg, #000000 0%, #2d2d2d 100%);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* Login page specific styles */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #000000 0%, #2d2d2d 50%, #1a1a1a 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Badge styles */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

.bg-warning {
    background: linear-gradient(45deg, #3d3d20 0%, #5a5a2d 100%) !important;
    color: #ffffcc !important;
}

.bg-success {
    background: linear-gradient(45deg, #203d20 0%, #2d5a2d 100%) !important;
    color: #ccffcc !important;
}

.bg-danger {
    background: linear-gradient(45deg, #3d2020 0%, #5a2d2d 100%) !important;
    color: #ffcccc !important;
}

/* Link styles */
a {
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--text-primary);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .container {
        width: 100%;
        max-width: none;
    }

    .card {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc !important;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }

    .dashboard-widget {
        padding: 1rem;
    }

    .dashboard-widget .icon {
        font-size: 2rem;
    }

    .dashboard-widget .value {
        font-size: 1.5rem;
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-color);
}

/* Animation for smooth transitions */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}