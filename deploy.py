#!/usr/bin/env python
"""
<PERSON>ript to deploy Django project to production
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

# Set production settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'accounting_system.settings')
os.environ.setdefault('DEBUG', 'False')

# Setup Django
django.setup()

def deploy():
    """Run deployment commands"""
    print("🚀 Starting deployment...")
    
    # Install requirements
    print("📦 Installing requirements...")
    os.system('pip install -r requirements.txt')
    
    # Run migrations
    print("🗄️ Running migrations...")
    execute_from_command_line(['manage.py', 'migrate'])
    
    # Collect static files
    print("📁 Collecting static files...")
    execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
    
    # Create admin user
    print("👤 Creating admin user...")
    execute_from_command_line(['manage.py', 'create_admin'])
    
    print("✅ Deployment completed successfully!")
    print("🌐 Your app is ready!")
    print("👤 Login: admin")
    print("🔑 Password: admin123")

if __name__ == '__main__':
    deploy()
