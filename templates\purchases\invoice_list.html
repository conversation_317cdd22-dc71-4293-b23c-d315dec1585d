{% extends 'purchases/base_purchases.html' %}

{% block breadcrumb %}
<li class="breadcrumb-item active">فواتير المشتريات</li>
{% endblock %}

{% block purchases_content %}
<div class="row mb-3">
    <div class="col">
        <a href="{% url 'purchases:invoice_create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-2"></i>
            فاتورة جديدة
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if invoices %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>المورد</th>
                        <th>إجمالي الفاتورة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td>{{ invoice.number }}</td>
                        <td>{{ invoice.date }}</td>
                        <td>{{ invoice.supplier.name }}</td>
                        <td>{{ invoice.total_amount }} ج.م</td>
                        <td>
                            {% if invoice.status == 'draft' %}
                            <span class="badge bg-warning">مسودة</span>
                            {% elif invoice.status == 'posted' %}
                            <span class="badge bg-success">مرحلة</span>
                            {% else %}
                            <span class="badge bg-danger">ملغاة</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'purchases:invoice_detail' invoice.pk %}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if invoice.status == 'draft' %}
                            <a href="{{ invoice.get_admin_url }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <p class="text-muted mb-0">لا توجد فواتير مشتريات</p>
        </div>
        {% endif %}
    </div>
</div>

{% if is_paginated %}
<nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابق</a>
        </li>
        {% endif %}

        {% for i in paginator.page_range %}
        <li class="page-item {% if page_obj.number == i %}active{% endif %}">
            <a class="page-link" href="?page={{ i }}">{{ i }}</a>
        </li>
        {% endfor %}

        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالي</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}