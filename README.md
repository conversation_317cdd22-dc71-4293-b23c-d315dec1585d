# الإشرافي - نظام المحاسبة المتقدم

نظام محاسبة شامل ومتقدم باللغة العربية مبني بـ Django

## المميزات

- 📊 **إدارة الحسابات المالية** - نظام شجرة حسابات متكامل
- 📦 **إدارة المخزون** - تتبع المنتجات والمخازن
- 🛒 **إدارة المبيعات** - فواتير ومدفوعات العملاء
- 🏪 **إدارة المشتريات** - فواتير ومدفوعات الموردين
- 📈 **التقارير المالية** - قوائم مالية شاملة
- 🎨 **تصميم أنيق** - واجهة سوداء وبيضاء جذابة
- 📱 **متجاوب** - يعمل على جميع الأجهزة

## التقنيات المستخدمة

- **Backend:** Django 5.2.1
- **Frontend:** Bootstrap 5 RTL + Custom CSS
- **Database:** PostgreSQL (Production) / SQLite (Development)
- **Deployment:** Railway / Render / PythonAnywhere

## التثبيت المحلي

```bash
# استنساخ المشروع
git clone https://github.com/elashrafy69/al-ishrafi-accounting.git
cd al-ishrafi-accounting

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل المايجريشن
python manage.py migrate

# إنشاء مستخدم مدير
python manage.py createsuperuser

# تشغيل الخادم
python manage.py runserver
```

## بيانات تسجيل الدخول الافتراضية

- **المستخدم:** admin
- **كلمة المرور:** admin123

## الترخيص

MIT License

## المطور

تم تطويره بواسطة elashrafy69
