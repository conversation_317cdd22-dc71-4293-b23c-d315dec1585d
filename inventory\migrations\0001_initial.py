# Generated by Django 5.1.6 on 2025-03-02 21:39

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Category Name')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='Category Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='inventory.category')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Product Code')),
                ('barcode', models.CharField(blank=True, max_length=50, verbose_name='Barcode')),
                ('name', models.CharField(max_length=200, verbose_name='Product Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('unit', models.CharField(choices=[('piece', 'Piece'), ('kg', 'Kilogram'), ('m', 'Meter'), ('l', 'Liter')], max_length=10, verbose_name='Unit')),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Purchase Price')),
                ('sale_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Sale Price')),
                ('min_stock', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Minimum Stock')),
                ('current_stock', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Current Stock')),
                ('image', models.ImageField(blank=True, upload_to='products/', verbose_name='Product Image')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='products', to='inventory.category')),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='Stocktaking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Stocktaking Date')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='Status')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stocktakings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Stocktaking',
                'verbose_name_plural': 'Stocktakings',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='StocktakingLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expected_quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Expected Quantity')),
                ('actual_quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Actual Quantity')),
                ('difference', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Difference')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stocktaking_lines', to='inventory.product')),
                ('stocktaking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='inventory.stocktaking')),
            ],
            options={
                'verbose_name': 'Stocktaking Line',
                'verbose_name_plural': 'Stocktaking Lines',
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Warehouse Name')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='Warehouse Code')),
                ('location', models.TextField(verbose_name='Location')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('manager', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Warehouse',
                'verbose_name_plural': 'Warehouses',
                'ordering': ['code'],
            },
        ),
        migrations.AddField(
            model_name='stocktaking',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stocktakings', to='inventory.warehouse'),
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Movement Date')),
                ('movement_type', models.CharField(choices=[('in', 'Stock In'), ('out', 'Stock Out')], max_length=3, verbose_name='Movement Type')),
                ('reason', models.CharField(choices=[('purchase', 'Purchase'), ('sale', 'Sale'), ('return_in', 'Return In'), ('return_out', 'Return Out'), ('adjustment', 'Stock Adjustment'), ('transfer', 'Stock Transfer')], max_length=20, verbose_name='Movement Reason')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Unit Price')),
                ('reference', models.CharField(max_length=50, verbose_name='Reference')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stock_movements', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stock_movements', to='inventory.product')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stock_movements', to='inventory.warehouse')),
            ],
            options={
                'verbose_name': 'Stock Movement',
                'verbose_name_plural': 'Stock Movements',
                'ordering': ['-date', '-created_at'],
            },
        ),
    ]
