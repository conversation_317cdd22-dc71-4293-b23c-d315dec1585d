#!/bin/bash

echo "🚀 Starting Al-Ishrafi Accounting System deployment..."

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Run migrations
echo "🗄️ Running database migrations..."
python manage.py migrate

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

# Create superuser
echo "👤 Creating admin user..."
python manage.py create_admin

echo "✅ Deployment completed successfully!"
echo "🌐 Starting web server..."

# Start the server
exec gunicorn accounting_system.wsgi:application --bind 0.0.0.0:${PORT:-8000} --workers 2
