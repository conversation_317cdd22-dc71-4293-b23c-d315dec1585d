{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS"}, "deploy": {"startCommand": "python manage.py migrate && python manage.py collectstatic --noinput && python manage.py create_admin && gunicorn accounting_system.wsgi:application --host 0.0.0.0 --port $PORT", "healthcheckPath": "/", "healthcheckTimeout": 100, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}}