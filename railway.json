{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS"}, "deploy": {"startCommand": "python manage.py migrate && python manage.py collectstatic --noinput && python manage.py shell -c \"from django.contrib.auth.models import User; User.objects.filter(username='admin').exists() or User.objects.create_superuser('admin', '<EMAIL>', 'admin123')\" && gunicorn accounting_system.wsgi:application --bind 0.0.0.0:$PORT", "healthcheckPath": "/", "healthcheckTimeout": 100, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}}