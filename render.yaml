services:
  - type: web
    name: al-ishrafi-accounting
    env: python
    buildCommand: "pip install -r requirements.txt && python manage.py collectstatic --noinput && python manage.py migrate"
    startCommand: "gunicorn accounting_system.wsgi:application"
    envVars:
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: "False"
      - key: ALLOWED_HOSTS
        value: "*"
      - key: DATABASE_URL
        fromDatabase:
          name: al-ishrafi-db
          property: connectionString
  - type: pserv
    name: al-ishrafi-db
    env: postgresql
    plan: free
