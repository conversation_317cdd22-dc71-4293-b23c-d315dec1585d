services:
  - type: web
    name: al-ishrafi-accounting
    env: python
    buildCommand: "pip install -r requirements.txt && python manage.py collectstatic --noinput && python manage.py migrate && python manage.py create_admin"
    startCommand: "gunicorn accounting_system.wsgi:application"
    envVars:
      - key: SECRET_KEY
        value: "django-insecure-production-key-al-ishrafi-2025"
      - key: DEBUG
        value: "False"
      - key: ALLOWED_HOSTS
        value: "*.onrender.com"
