# Generated by Django 5.1.6 on 2025-03-02 22:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='account',
            options={'ordering': ['code'], 'verbose_name': 'حساب', 'verbose_name_plural': 'الحسابات'},
        ),
        migrations.AlterModelOptions(
            name='accounttype',
            options={'verbose_name': 'نوع الحساب', 'verbose_name_plural': 'أنواع الحسابات'},
        ),
        migrations.AlterModelOptions(
            name='financialstatement',
            options={'ordering': ['-date'], 'verbose_name': 'قائمة مالية', 'verbose_name_plural': 'القوائم المالية'},
        ),
        migrations.AlterModelOptions(
            name='fiscalyear',
            options={'ordering': ['-start_date'], 'verbose_name': 'سنة مالية', 'verbose_name_plural': 'السنوات المالية'},
        ),
        migrations.AlterModelOptions(
            name='journalentry',
            options={'ordering': ['-date', '-created_at'], 'verbose_name': 'قيد يومية', 'verbose_name_plural': 'قيود اليومية'},
        ),
        migrations.AlterField(
            model_name='account',
            name='balance',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الحالي'),
        ),
        migrations.AlterField(
            model_name='account',
            name='code',
            field=models.CharField(max_length=10, unique=True, verbose_name='كود الحساب'),
        ),
        migrations.AlterField(
            model_name='account',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='account',
            name='description',
            field=models.TextField(blank=True, verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='account',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='نشط'),
        ),
        migrations.AlterField(
            model_name='account',
            name='name',
            field=models.CharField(max_length=100, verbose_name='اسم الحساب'),
        ),
        migrations.AlterField(
            model_name='account',
            name='normal_balance',
            field=models.CharField(choices=[('debit', 'مدين'), ('credit', 'دائن')], max_length=6, verbose_name='الرصيد الطبيعي'),
        ),
        migrations.AlterField(
            model_name='account',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='accounts.account', verbose_name='الحساب الرئيسي'),
        ),
        migrations.AlterField(
            model_name='account',
            name='type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='accounts', to='accounts.accounttype', verbose_name='نوع الحساب'),
        ),
        migrations.AlterField(
            model_name='account',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='accounttype',
            name='code',
            field=models.CharField(max_length=2, verbose_name='كود النوع'),
        ),
        migrations.AlterField(
            model_name='accounttype',
            name='description',
            field=models.TextField(blank=True, verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='accounttype',
            name='name',
            field=models.CharField(max_length=50, verbose_name='اسم النوع'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='data',
            field=models.JSONField(verbose_name='البيانات'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='date',
            field=models.DateField(verbose_name='تاريخ القائمة'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='fiscal_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='statements', to='accounts.fiscalyear', verbose_name='السنة المالية'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='generated_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='generated_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='type',
            field=models.CharField(choices=[('balance_sheet', 'الميزانية العمومية'), ('income_statement', 'قائمة الدخل'), ('cash_flow', 'قائمة التدفقات النقدية')], max_length=20, verbose_name='نوع القائمة'),
        ),
        migrations.AlterField(
            model_name='fiscalyear',
            name='end_date',
            field=models.DateField(verbose_name='تاريخ النهاية'),
        ),
        migrations.AlterField(
            model_name='fiscalyear',
            name='is_closed',
            field=models.BooleanField(default=False, verbose_name='مغلقة'),
        ),
        migrations.AlterField(
            model_name='fiscalyear',
            name='name',
            field=models.CharField(max_length=50, verbose_name='السنة المالية'),
        ),
        migrations.AlterField(
            model_name='fiscalyear',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='fiscalyear',
            name='start_date',
            field=models.DateField(verbose_name='تاريخ البداية'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='journal_entries', to='accounts.account', verbose_name='الحساب'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='amount',
            field=models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='journal_entries', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='date',
            field=models.DateField(verbose_name='تاريخ القيد'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='description',
            field=models.TextField(verbose_name='البيان'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='entry_type',
            field=models.CharField(choices=[('debit', 'مدين'), ('credit', 'دائن')], max_length=6, verbose_name='نوع القيد'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='reference',
            field=models.CharField(max_length=50, verbose_name='المرجع'),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
    ]
