{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشرافي | {% block title %}{% endblock %}</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <!-- Dark Theme CSS -->
    <style>
        /* Dark Theme Variables */
        :root {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3a3a3a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-muted: #888888;
            --accent-color: #ffffff;
            --border-color: #404040;
            --shadow-dark: 0 4px 20px rgba(0, 0, 0, 0.3);
            --shadow-light: 0 2px 10px rgba(255, 255, 255, 0.1);
        }

        /* General styles */
        body {
            font-family: 'Cairo', sans-serif !important;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%) !important;
            color: var(--text-primary) !important;
            min-height: 100vh;
        }

        /* Navbar styles */
        .navbar {
            background: linear-gradient(90deg, #000000 0%, #2d2d2d 100%) !important;
            box-shadow: var(--shadow-dark) !important;
            border-bottom: 1px solid var(--border-color) !important;
        }

        .navbar-brand {
            color: var(--text-primary) !important;
            font-size: 1.5rem !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
        }

        .navbar-nav .nav-link {
            color: var(--text-secondary) !important;
            transition: all 0.3s ease !important;
        }

        .navbar-nav .nav-link:hover {
            color: var(--text-primary) !important;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5) !important;
        }

        /* Card styles */
        .card {
            background: var(--bg-secondary) !important;
            border: 1px solid var(--border-color) !important;
            border-radius: 15px !important;
            box-shadow: var(--shadow-dark) !important;
            transition: transform 0.3s ease, box-shadow 0.3s ease !important;
        }

        .card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4) !important;
        }

        .card-header {
            background: linear-gradient(90deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%) !important;
            border-bottom: 1px solid var(--border-color) !important;
            color: var(--text-primary) !important;
        }

        .card-body {
            background: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
        }

        /* Dashboard widgets */
        .dashboard-widget {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
            border: 1px solid var(--border-color) !important;
            border-radius: 15px !important;
            box-shadow: var(--shadow-dark) !important;
            transition: all 0.3s ease !important;
            position: relative;
            overflow: hidden;
        }

        .dashboard-widget::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ffffff 0%, #888888 100%);
        }

        .dashboard-widget:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4) !important;
        }

        .dashboard-widget .icon {
            color: var(--text-primary) !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
        }

        .dashboard-widget .title {
            color: var(--text-secondary) !important;
        }

        .dashboard-widget .value {
            color: var(--text-primary) !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        /* Form styles */
        .form-control {
            background: var(--bg-tertiary) !important;
            border: 1px solid var(--border-color) !important;
            color: var(--text-primary) !important;
        }

        .form-control:focus {
            background: var(--bg-secondary) !important;
            border-color: var(--accent-color) !important;
            color: var(--text-primary) !important;
            box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1) !important;
        }

        /* Button styles */
        .btn-primary {
            background: linear-gradient(45deg, #000000 0%, #404040 100%) !important;
            border: 1px solid var(--border-color) !important;
            color: var(--text-primary) !important;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #404040 0%, #000000 100%) !important;
            transform: translateY(-2px) !important;
        }

        /* Table styles */
        .table {
            background: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
            border: 1px solid var(--border-color) !important;
        }

        .table thead th {
            background: var(--bg-tertiary) !important;
            color: var(--text-primary) !important;
            border-bottom: 2px solid var(--border-color) !important;
        }

        .table tbody tr {
            border-bottom: 1px solid var(--border-color) !important;
        }

        .table tbody tr:hover {
            background: var(--bg-tertiary) !important;
        }

        /* Footer styles */
        .footer {
            background: linear-gradient(90deg, #000000 0%, #2d2d2d 100%) !important;
            border-top: 1px solid var(--border-color) !important;
            color: var(--text-secondary) !important;
        }

        /* Dropdown styles */
        .dropdown-menu {
            background: var(--bg-secondary) !important;
            border: 1px solid var(--border-color) !important;
        }

        .dropdown-item {
            color: var(--text-secondary) !important;
        }

        .dropdown-item:hover {
            background: var(--bg-tertiary) !important;
            color: var(--text-primary) !important;
        }

        /* Links */
        a {
            color: var(--text-primary) !important;
        }

        a:hover {
            color: var(--text-primary) !important;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5) !important;
        }

        /* Login container */
        .login-container {
            min-height: 100vh !important;
            background: linear-gradient(135deg, #000000 0%, #2d2d2d 50%, #1a1a1a 100%) !important;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                الإشرافي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">
                            <i class="fas fa-cog"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-shopping-cart"></i>
                            المبيعات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'sales:invoice_create' %}">فاتورة جديدة</a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:invoice_list' %}">قائمة الفواتير</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/sales/customer/">العملاء</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-truck"></i>
                            المشتريات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'purchases:invoice_create' %}">فاتورة جديدة</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:invoice_list' %}">قائمة الفواتير</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/purchases/supplier/">الموردين</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes"></i>
                            المخزون
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/inventory/product/">المنتجات</a></li>
                            <li><a class="dropdown-item" href="/admin/inventory/category/">التصنيفات</a></li>
                            <li><a class="dropdown-item" href="/admin/inventory/stockmovement/">حركة المخزون</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calculator"></i>
                            الحسابات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/accounts/account/">دليل الحسابات</a></li>
                            <li><a class="dropdown-item" href="/admin/accounts/journalentry/">القيود اليومية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/accounts/financialstatement/">التقارير المالية</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i>
                            {{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/admin/password_change/">تغيير كلمة المرور</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">تسجيل الدخول</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2025 الإشرافي. جميع الحقوق محفوظة</span>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="/static/js/main.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>